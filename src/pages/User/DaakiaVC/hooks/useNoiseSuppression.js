import { useEffect, useRef } from 'react';
import { Track } from 'livekit-client';
import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';
import { useNoiseSuppressionContext } from '../context/indexContext';


export const useNoiseSuppression = (room, deviceIdAudio) => {
  const { isNoiseSuppressionEnabled, setIsNoiseSuppressionEnabled } = useNoiseSuppressionContext();
  const noiseProcessor = useRef(null);
  const originalTrack = useRef(null);
  const savedDeviceId = useRef(null); // Track the device ID when NS was applied

  // Helper function to get mic state
  const getMicState = () => {
    const micPublication = room?.localParticipant?.getTrackPublication(Track.Source.Microphone);
    const currentTrackDeviceId = micPublication?.track?.mediaStreamTrack?.getSettings()?.deviceId;

    // Device changed if we have a saved device ID and current device is different
    const isDeviceChanged = savedDeviceId.current && currentTrackDeviceId &&
                           savedDeviceId.current !== currentTrackDeviceId;

    return {
      micPublication,
      isEnabled: micPublication?.isEnabled || false,
      hasTrack: !!micPublication?.track,
      isMuted: micPublication?.isMuted || false,
      isPublished: micPublication?.track && !micPublication?.isMuted,
      hasNoiseProcessor: !!noiseProcessor.current,
      currentTrackDeviceId,
      savedDeviceId: savedDeviceId.current,
      selectedDeviceId: deviceIdAudio,
      isDeviceChanged
    };
  };

  // Function to apply noise suppression
  const applyNoiseSuppression = async () => {
    try {
      const { micPublication, currentTrackDeviceId } = getMicState();
      const localAudioTrack = micPublication.track;

      console.log('� Applying noise suppression...');

      // Store original track and save device ID
      originalTrack.current = localAudioTrack.mediaStreamTrack;
      savedDeviceId.current = currentTrackDeviceId; // Save device ID when NS is applied
      console.log('💾 Original track stored and device ID saved:', currentTrackDeviceId);

      // Create and start noise processor
      noiseProcessor.current = new NoiseSuppressionProcessor();
      const processedTrack = await noiseProcessor.current.startProcessing(
        localAudioTrack.mediaStreamTrack
      );

      if (processedTrack) {
        await localAudioTrack.replaceTrack(processedTrack, true);
        console.log('✅ Noise suppression applied successfully');
      }
    } catch (error) {
      console.error('❌ Error applying noise suppression:', error);
    }
  };

  // Function to stop noise suppression
  const stopNoiseSuppression = async () => {
    try {
      if (noiseProcessor.current) {
        console.log('🔇 Stopping noise suppression...');

        const { micPublication } = getMicState();

        if (micPublication?.track && originalTrack.current) {
          // Restore original track
          await micPublication.track.replaceTrack(originalTrack.current, true);
          console.log('✅ Original track restored');
        }

        // Clean up processor
        noiseProcessor.current.stopProcessing();
        noiseProcessor.current = null;
        originalTrack.current = null;
        savedDeviceId.current = null; // Clear saved device ID
        console.log('🧹 Noise processor cleaned up');
      }
    } catch (error) {
      console.error('❌ Error stopping noise suppression:', error);
    }
  };

  // Main logic function with clear if-else conditions
  const handleNoiseSuppressionLogic = async () => {
    const {
      isEnabled,
      hasTrack,
      isMuted,
      isPublished,
      hasNoiseProcessor,
      currentTrackDeviceId,
      selectedDeviceId,
      isDeviceChanged
    } = getMicState();

    // Log current state
    console.log('🎤 Microphone State:', {
      isEnabled,
      hasTrack,
      isMuted,
      isPublished,
      isNoiseSuppressionEnabled,
      hasNoiseProcessor,
      currentTrackDeviceId,
      savedDeviceId: getMicState().savedDeviceId,
      selectedDeviceId,
      isDeviceChanged
    });

    // Case 1: NS Toggle is OFF
    if (!isNoiseSuppressionEnabled) {
      if (hasNoiseProcessor) {
        console.log('🛑 NS toggle OFF - stopping noise suppression');
        await stopNoiseSuppression();
      }
      return;
    }

    // Case 1.5: Device changed - handle with toggle disable/enable
    if (isDeviceChanged && hasNoiseProcessor) {
      console.log('🔄 Device changed from', getMicState().savedDeviceId, 'to', currentTrackDeviceId);

      // Step 1: Stop current NS (this clears savedDeviceId)
      await stopNoiseSuppression();

      // Step 2: Disable NS toggle
      console.log('🔄 Disabling NS toggle temporarily...');
      setIsNoiseSuppressionEnabled(false);

      // Step 3: Wait a bit for device to settle
      setTimeout(() => {
        console.log('🔄 Re-enabling NS toggle after device change...');
        setIsNoiseSuppressionEnabled(true);
        // The useEffect will run again and apply NS with new device ID
      }, 700);

      return;
    }

    // Case 2: NS Toggle is ON but no mic track
    if (!hasTrack) {
      console.log('🎛️ NS ON but no mic track - waiting...');
      return;
    }

    // Case 3: NS Toggle is ON, mic exists but muted
    if (isMuted) {
      if (hasNoiseProcessor) {
        console.log('� Mic muted but NS already applied - keeping NS active');
        return;
      } else {
        console.log('🔇 Mic muted and no NS yet - waiting for unmute...');
        return;
      }
    }

    // Case 4: NS Toggle is ON, mic unmuted and published
    if (isPublished) {
      if (hasNoiseProcessor) {
        console.log('✅ Mic unmuted and NS already working - no action needed');
        return;
      } else {
        console.log('🔊 Mic unmuted and no NS yet - applying noise suppression with delay...');

        // Add delay before applying noise suppression
        setTimeout(async () => {
          try {
            // Double check conditions are still valid after delay
            const currentState = getMicState();
            if (currentState.isPublished && isNoiseSuppressionEnabled && !noiseProcessor.current) {
              console.log('🔊 Now applying noise suppression after delay...');
              await applyNoiseSuppression();
            } else {
              console.log('⏹️ Conditions changed during delay, skipping noise suppression');
            }
          } catch (error) {
            console.error('❌ Delayed noise suppression error:', error);
          }
        }, 1000); // 1 second delay

        return;
      }
    }

    // Case 5: Mic disabled
    if (!isEnabled) {
      console.log('🎤 Mic disabled - no action needed');
    }
  };

  // Main useEffect
  useEffect(() => {
    if (!room?.localParticipant) return;
    handleNoiseSuppressionLogic();
  }, [
    room?.localParticipant?.getTrackPublication(Track.Source.Microphone)?.isEnabled,
    room?.localParticipant?.getTrackPublication(Track.Source.Microphone)?.track,
    room?.localParticipant?.getTrackPublication(Track.Source.Microphone)?.isMuted,
    deviceIdAudio,
    isNoiseSuppressionEnabled
  ]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      console.log('🧹 Component cleanup - stopping noise suppression');
      stopNoiseSuppression();
    };
  }, []);

  return {
    isNoiseSuppressionActive: !!noiseProcessor.current,
    hasOriginalTrack: !!originalTrack.current,
    stopNoiseSuppression
  };
};
